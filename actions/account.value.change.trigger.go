package actions

import (
	"github.com/metadiv-tech/workflow"
)

type AccountValueChangeTriggerRequest struct {
	AccountId string `json:"account_id"`
	FieldName string `json:"field_name"`
	NewValue  string `json:"new_value"`
}

type AccountValueChangeTriggerResponse struct {
	AccountId string `json:"account_id"`
	FieldName string `json:"field_name"`
	NewValue  string `json:"new_value"`
}

var AccountValueChangeTrigger = workflow.NewTriggerAction[AccountValueChangeTriggerRequest, AccountValueChangeTriggerResponse]("account.value.change.trigger").
	WithName("accountValueChangeTrigger").
	WithCategory("Salesforce").
	WithFunction(func(ctx *workflow.Context, input AccountValueChangeTriggerRequest) (AccountValueChangeTriggerResponse, error) {
		return AccountValueChangeTriggerResponse(input), nil
	}).Build()
