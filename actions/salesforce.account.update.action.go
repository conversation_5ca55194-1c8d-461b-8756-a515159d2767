package actions

import (
	"github.com/metadiv-tech/mod_salesforce/services/salesforce_account_service"
	"github.com/metadiv-tech/workflow"
)

type SalesforceAccountUpdateRequest struct {
	AccountId string                 `json:"account_id" binding:"required"`
	Updates   map[string]interface{} `json:"updates" binding:"required"`
}

type SalesforceAccountUpdateResponse struct {
	Success   bool   `json:"success"`
	AccountId string `json:"account_id"`
	Message   string `json:"message,omitempty"`
}

var SalesforceAccountUpdateAction = workflow.NewAction[SalesforceAccountUpdateRequest, SalesforceAccountUpdateResponse]("salesforce.account.update.action").
	WithName("salesforceAccountUpdate").
	WithCategory("Salesforce").
	WithFunction(func(ctx *workflow.Context, input SalesforceAccountUpdateRequest) (SalesforceAccountUpdateResponse, error) {
		// Create update account service
		updateService := salesforce_account_service.NewUpdateAccountService(ctx.DB)

		// Convert input to service request format
		request := salesforce_account_service.UpdateAccountRequest{
			AccountId: input.AccountId,
			Updates:   input.Updates,
		}

		// Update the account using the service
		response, err := updateService.UpdateAccount(ctx.WorkspaceId, request)
		if err != nil {
			return SalesforceAccountUpdateResponse{
				Success:   false,
				AccountId: input.AccountId,
				Message:   err.Error(),
			}, nil
		}

		// Convert service response to action response
		return SalesforceAccountUpdateResponse{
			Success:   response.Success,
			AccountId: response.AccountId,
			Message:   response.Message,
		}, nil
	}).Build()
