package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
)

type SalesforceAccountEvent struct {
	base.ModelWorkspace[SalesforceAccountEvent]
	IncomingData types.Json[[]map[string]any] `json:"incoming_data"`
	Result       types.LongText               `json:"result"`
	Status       types.Varchar                `json:"status"`
}

func (s *SalesforceAccountEvent) TableName() string {
	return "salesforce_account_events"
}

type SalesforceAccountEventDTO struct {
	base.ModelWorkspaceDTO
	IncomingData []map[string]any `json:"incoming_data"`
	Result       string           `json:"result"`
	Status       string           `json:"status"`
}

func (s *SalesforceAccountEvent) DTO() *SalesforceAccountEventDTO {
	d := &SalesforceAccountEventDTO{
		ModelWorkspaceDTO: *s.ModelWorkspace.DTO(),
		Result:            s.Result.Get(),
		Status:            s.Status.Get(),
	}
	incomingDataPtr := s.IncomingData.Get()
	if incomingDataPtr != nil {
		d.IncomingData = *incomingDataPtr
	}
	return d
}
