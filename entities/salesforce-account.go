package entities

import (
	"github.com/metadiv-tech/metaorm/base"
	"github.com/metadiv-tech/metaorm/types"
)

type SalesforceAccount struct {
	base.ModelWorkspace[SalesforceAccount]
	SalesforceId    types.Varchar              `json:"salesforce_id"`
	Contents        types.Json[map[string]any] `json:"contents"`
	ChangedContents types.Json[map[string]any] `json:"changed_contents"`
	ProcessStatus   types.Varchar              `json:"process_status"`
}

func (s *SalesforceAccount) TableName() string {
	return "salesforce_accounts"
}

type SalesforceAccountDTO struct {
	base.ModelWorkspaceDTO
	SalesforceId    string         `json:"salesforce_id"`
	Contents        map[string]any `json:"contents"`
	ChangedContents map[string]any `json:"changed_contents"`
	ProcessStatus   string         `json:"process_status"`
}

func (s *SalesforceAccount) DTO() *SalesforceAccountDTO {
	d := &SalesforceAccountDTO{
		ModelWorkspaceDTO: *s.ModelWorkspace.DTO(),
		SalesforceId:      s.SalesforceId.Get(),
		ProcessStatus:     s.ProcessStatus.Get(),
	}
	contentsPtr := s.Contents.Get()
	if contentsPtr != nil {
		d.Contents = *contentsPtr
	}
	changedContentsPtr := s.ChangedContents.Get()
	if changedContentsPtr != nil {
		d.ChangedContents = *changedContentsPtr
	}
	return d
}
