package mod_salesforce

import (
	"github.com/metadiv-tech/metagin"
	_ "github.com/metadiv-tech/mod_salesforce/actions"
	"github.com/metadiv-tech/mod_salesforce/endpoints/salesforce_account"
	"github.com/metadiv-tech/mod_salesforce/entities"
	_ "github.com/metadiv-tech/mod_salesforce/system_settings"
)

var ModuleSalesForce = metagin.NewModule("salesforce", "v1", func(m *metagin.Module) {
	m.RegisterMigration(
		&entities.SalesforceAccount{},
		&entities.SalesforceAccountEvent{},
	)
	m.RegisterHandler(
		salesforce_account.ApiSalesforceAccountWebhookSecretLink,
		salesforce_account.ApiSalesforceAccountWebhook,
		salesforce_account.ApiSalesforceAccountList,
		salesforce_account.ApiSalesforceAccountEventList,
		salesforce_account.CronSalesforceAccountProcessEvent,
		salesforce_account.CronSalesforceAccountProcess,
	)
})
