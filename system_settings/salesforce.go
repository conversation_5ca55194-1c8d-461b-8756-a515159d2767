package system_settings

import "github.com/metadiv-tech/mod_utils/services/system_setting_service"

var (
	KeySystemBackendUrl               = system_setting_service.NewString("system.backend_url", "https://api.metadiv.io", false, false, true)
	KeySalesforceAccountWebhookSecret = system_setting_service.NewString("salesforce.account.webhook.secret", "", true, true, false)
)

var (
	KeySalesforceClientId     = system_setting_service.NewString("salesforce.client_id", "", true, true, false)
	KeySalesforceClientSecret = system_setting_service.NewString("salesforce.client_secret", "", true, true, false)
	KeySalesforceInstance     = system_setting_service.NewString("salesforce.instance", "", true, true, false)
)

var (
	KeySalesforceRefreshToken = system_setting_service.NewString("salesforce.refresh_token", "", true, true, false)
)
