package salesforce_account

import (
	"fmt"

	"github.com/metadiv-tech/logger"
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_salesforce/configs"
	"github.com/metadiv-tech/mod_salesforce/entities"
	"github.com/metadiv-tech/mod_workflow/services/workflow_service"
)

var CronSalesforceAccountProcess = metagin.Cron("salesforceAccountProcess").
	EveryMinute(1).
	Handler(func(db *metaorm.DB) {
		accounts, err := new(entities.SalesforceAccount).RepoWithoutWorkspace(db).FindAll(db.Eq("process_status", configs.SalesforceAccountProcessStatusPending))
		if err != nil {
			return
		}
		if len(accounts) == 0 {
			return
		}
		for i := range accounts {
			accounts[i].ProcessStatus.Set(configs.SalesforceAccountProcessStatusProcessing)
		}
		accounts, err = new(entities.SalesforceAccount).RepoWithoutWorkspace(db).SaveAll(accounts)
		if err != nil {
			return
		}
		for i := range accounts {
			// Process changed contents and trigger workflows for each changed field
			changedContentsPtr := accounts[i].ChangedContents.Get()
			if changedContentsPtr != nil {
				changedContents := *changedContentsPtr

				// Find all workflows with account value change triggers for this workspace
				workflowsWithTriggers, err := workflow_service.FindWorkflowsByTriggerActionUUID(
					db,
					accounts[i].WorkspaceId.Get(),
					"account.value.change.trigger",
				)
				if err != nil {
					logger.Error(fmt.Sprintf("Failed to find workflows for account %s: %v\n",
						accounts[i].SalesforceId.Get(), err))
					continue
				}

				// For each changed field, find and execute matching trigger nodes
				for fieldName, newValue := range changedContents {
					newValueStr := fmt.Sprintf("%v", newValue)

					// Create input data for the trigger node
					inputData := map[string]any{
						"account_id": accounts[i].SalesforceId.Get(),
						"field_name": fieldName,
						"new_value":  newValueStr,
					}

					// Check each workflow to find trigger nodes monitoring this field
					for _, workflowWithTriggers := range workflowsWithTriggers {
						for _, triggerNode := range workflowWithTriggers.TargetTriggerNodes {
							// Check if this trigger node is configured to monitor the changed field
							inputsPtr := triggerNode.Inputs.Get()
							if inputsPtr != nil {
								inputs := *inputsPtr
								// Check if the trigger field matches the changed field
								if triggerField, exists := inputs["field_name"]; exists {
									if triggerFieldStr, ok := triggerField.(string); ok && triggerFieldStr == fieldName {
										// This trigger node is monitoring the changed field, execute workflow from this node
										_, err := workflow_service.ExecuteWorkflowFromNode(
											db,
											accounts[i].WorkspaceId.Get(),
											&triggerNode,
											inputData,
										)
										if err != nil {
											logger.Error(fmt.Sprintf("Failed to execute workflow %d from node %s for account %s field %s: %v\n",
												workflowWithTriggers.Workflow.ID, triggerNode.UUID.Get(),
												accounts[i].SalesforceId.Get(), fieldName, err))
										} else {
											logger.Info(fmt.Sprintf("Successfully executed workflow %d from node %s for account %s field %s\n",
												workflowWithTriggers.Workflow.ID, triggerNode.UUID.Get(),
												accounts[i].SalesforceId.Get(), fieldName))
										}
									}
								}
							}
						}
					}
				}
			}
			accounts[i].ProcessStatus.Set(configs.SalesforceAccountProcessStatusSuccess)
		}
		new(entities.SalesforceAccount).RepoWithoutWorkspace(db).SaveAll(accounts)
	}).Build()
