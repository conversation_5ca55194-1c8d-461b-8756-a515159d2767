package salesforce_account

import (
	"fmt"

	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_salesforce/system_settings"
	"github.com/metadiv-tech/mod_utils/services/system_setting_service"
	"github.com/metadiv-tech/nanoid"
)

type SalesforceAccountWebhookSecretLinkResponse struct {
	Link string `json:"link"`
}

var ApiSalesforceAccountWebhookSecretLink = metagin.Get[base.Empty, SalesforceAccountWebhookSecretLinkResponse]("salesforceWebhookSecretLink").
	Route("/salesforce/account/webhook/secret-link").
	Handler(func(ctx metagin.IContext[base.Empty, SalesforceAccountWebhookSecretLinkResponse]) {

		adminQuery := system_setting_service.NewAdminQuery(ctx.DB())
		backendUrl := adminQuery.GetByKey(system_settings.KeySystemBackendUrl).String()

		workspaceQuery := system_setting_service.NewWorkspaceQuery(ctx.DB(), ctx.WorkspaceId())
		secretSetting := workspaceQuery.GetByKey(system_settings.KeySalesforceAccountWebhookSecret)
		secret := secretSetting.String()
		if secret == "" {
			var err error
			secret, err = nanoid.NewSafe()
			if err != nil {
				ctx.Error(err)
				return
			}
			secretSetting.SetString(secret)
			secretSetting.Repo(ctx.DB(), ctx.WorkspaceId()).Save(secretSetting)
		}

		link := fmt.Sprintf("%s/v1/public/salesforce/account/webhook/%s", backendUrl, secret)
		ctx.OK(&SalesforceAccountWebhookSecretLinkResponse{
			Link: link,
		})
	}).Build()
