package salesforce_account

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_salesforce/entities"
)

var ApiSalesforceAccountList = metagin.Get[base.RequestListing, []entities.SalesforceAccountDTO]("salesforceAccountList").
	Route("/salesforce/account").
	Handler(func(ctx metagin.IContext[base.RequestListing, []entities.SalesforceAccountDTO]) {
		accounts, page, err := new(entities.SalesforceAccount).Repo(ctx.DB(), ctx.WorkspaceId()).FindAllComplex(
			&ctx.Request().Pagination,
			&ctx.Request().Sorting,
			ctx.Request().SimilarKeyword("salesforce_id"),
		)
		if err != nil {
			ctx.Error(err)
			return
		}
		ds := make([]entities.SalesforceAccountDTO, len(accounts))
		for i := range accounts {
			ds[i] = *accounts[i].DTO()
		}
		ctx.OK(&ds, page)
	}).Build()
