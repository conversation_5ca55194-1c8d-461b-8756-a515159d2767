package salesforce_account

import (
	"reflect"

	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_salesforce/configs"
	"github.com/metadiv-tech/mod_salesforce/entities"
)

var CronSalesforceAccountProcessEvent = metagin.Cron("salesforceAccountProcessEvent").
	EveryMinute(1).
	Handler(func(db *metaorm.DB) {

		events, err := new(entities.SalesforceAccountEvent).RepoWithoutWorkspace(db).FindAll(db.Eq("status", configs.SalesforceAccountEventStatusPending))
		if err != nil {
			return
		}
		if len(events) == 0 {
			return
		}
		for i := range events {
			events[i].Status.Set(configs.SalesforceAccountEventStatusProcessing)
		}

		events, err = new(entities.SalesforceAccountEvent).RepoWithoutWorkspace(db).SaveAll(events)
		if err != nil {
			return
		}

		for i := range events {
			dataPtr := events[i].IncomingData.Get()
			if dataPtr == nil {
				events[i].Status.Set(configs.SalesforceAccountEventStatusError)
				events[i].Result.Set("No data to process")
				continue
			}
			data := *dataPtr
			for _, item := range data {
				salesforceId, ok := item["Id"].(string)
				if !ok {
					events[i].Status.Set(configs.SalesforceAccountEventStatusError)
					events[i].Result.Set("No salesforce id to process")
					continue
				}
				account, err := new(entities.SalesforceAccount).Repo(db, events[i].WorkspaceId.Get()).FindOne(db.Eq("salesforce_id", salesforceId))
				if err != nil {
					events[i].Status.Set(configs.SalesforceAccountEventStatusError)
					events[i].Result.Set("Error: " + err.Error())
					continue
				}
				if account == nil {
					account = new(entities.SalesforceAccount)
					account.SalesforceId.Set(salesforceId)
				}

				var oldContent = make(map[string]any)

				oldContentPtr := account.Contents.Get()
				if oldContentPtr != nil {
					oldContent = *oldContentPtr
				}

				changedContent := make(map[string]any)
				for key, value := range item {
					if !reflect.DeepEqual(oldContent[key], value) {
						changedContent[key] = value
					}
				}

				account.Contents.Set(item)
				account.ChangedContents.Set(changedContent)
				account.ProcessStatus.Set(configs.SalesforceAccountProcessStatusPending)
				account.Repo(db, events[i].WorkspaceId.Get()).Save(account)
				events[i].Status.Set(configs.SalesforceAccountEventStatusSuccess)
				events[i].Result.Set("Account processed successfully")
			}
		}

		new(entities.SalesforceAccountEvent).RepoWithoutWorkspace(db).SaveAll(events)
	}).Build()
