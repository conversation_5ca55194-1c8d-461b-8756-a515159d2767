package salesforce_account

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_salesforce/configs"
	"github.com/metadiv-tech/mod_salesforce/entities"
	"github.com/metadiv-tech/mod_salesforce/errors"
	utilEntities "github.com/metadiv-tech/mod_utils/entities"
)

type SalesforceAccountWebhookRequest struct {
	Secret string           `uri:"secret"`
	Data   []map[string]any `json:"data"`
}

var ApiSalesforceAccountWebhook = metagin.Post[SalesforceAccountWebhookRequest, base.Empty]("salesforceAccountWebhook").
	PublicRoute("/salesforce/account/webhook/:secret").
	Handler(func(ctx metagin.IPublicContext[SalesforceAccountWebhookRequest, base.Empty]) {

		systemSetting, err := new(utilEntities.SystemSetting).RepoWithoutWorkspace(ctx.DB()).FindOne(ctx.DB().Eq("*value", ctx.Request().Secret))
		if err != nil {
			ctx.Error(err)
			return
		}
		if systemSetting == nil {
			ctx.Error(errors.ErrInvalidSecret)
			return
		}

		workspaceId := systemSetting.WorkspaceId.Get()

		event := new(entities.SalesforceAccountEvent)
		event.IncomingData.Set(ctx.Request().Data)
		event.Status.Set(configs.SalesforceAccountEventStatusPending)
		event.Repo(ctx.DB(), workspaceId).Save(event)
		ctx.OK(nil)
	}).Build()
