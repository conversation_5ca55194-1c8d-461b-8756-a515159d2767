package salesforce_account

import (
	"github.com/metadiv-tech/metagin"
	"github.com/metadiv-tech/metagin/base"
	"github.com/metadiv-tech/mod_salesforce/entities"
)

var ApiSalesforceAccountEventList = metagin.Get[base.RequestListing, []entities.SalesforceAccountEventDTO]("salesforceAccountEventList").
	Route("/salesforce/account/event").
	Handler(func(ctx metagin.IContext[base.RequestListing, []entities.SalesforceAccountEventDTO]) {
		events, page, err := new(entities.SalesforceAccountEvent).Repo(ctx.DB(), ctx.WorkspaceId()).FindAllComplex(
			&ctx.Request().Pagination,
			&ctx.Request().Sorting,
			ctx.Request().SimilarKeyword("incoming_data"),
		)
		if err != nil {
			ctx.Error(err)
			return
		}
		ds := make([]entities.SalesforceAccountEventDTO, len(events))
		for i := range events {
			ds[i] = *events[i].DTO()
		}
		ctx.OK(&ds, page)
	}).Build()
