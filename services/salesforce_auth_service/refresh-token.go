package salesforce_auth_service

import (
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"

	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_salesforce/system_settings"
	"github.com/metadiv-tech/mod_utils/services/system_setting_service"
)

type TokenResponse struct {
	AccessToken string `json:"access_token"`
	TokenType   string `json:"token_type"`
	Scope       string `json:"scope"`
}

// RefreshAccessToken refreshes the Salesforce access token using the refresh token
// This function should be called before every Salesforce API call to ensure valid access token
func RefreshAccessToken(db *metaorm.DB, workspaceId uint) (string, error) {
	// Get system settings using workspace query
	workspaceQuery := system_setting_service.NewWorkspaceQuery(db, workspaceId)

	clientId := workspaceQuery.GetByKey(system_settings.KeySalesforceClientId).String()
	if clientId == "" {
		return "", fmt.Errorf("SALESFORCE_CLIENT_ID must be configured in system settings")
	}

	clientSecret := workspaceQuery.GetByKey(system_settings.KeySalesforceClientSecret).String()
	if clientSecret == "" {
		return "", fmt.Errorf("SALESFORCE_CLIENT_SECRET must be configured in system settings")
	}

	refreshToken := workspaceQuery.GetByKey(system_settings.KeySalesforceRefreshToken).String()
	if refreshToken == "" {
		return "", fmt.Errorf("SALESFORCE_REFRESH_TOKEN must be configured in system settings")
	}

	instance := workspaceQuery.GetByKey(system_settings.KeySalesforceInstance).String()
	if instance == "" {
		return "", fmt.Errorf("SALESFORCE_INSTANCE must be configured in system settings")
	}

	// Prepare token refresh request
	tokenURL := fmt.Sprintf("%s/services/oauth2/token", instance)

	data := url.Values{}
	data.Set("grant_type", "refresh_token")
	data.Set("refresh_token", refreshToken)
	data.Set("client_id", clientId)
	data.Set("client_secret", clientSecret)

	// Make the request
	req, err := http.NewRequest("POST", tokenURL, strings.NewReader(data.Encode()))
	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Set("Accept", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", fmt.Errorf("failed to make token refresh request: %w", err)
	}
	defer resp.Body.Close()

	// Read response
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("token refresh failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse response
	var tokenResp TokenResponse
	if err := json.Unmarshal(body, &tokenResp); err != nil {
		return "", fmt.Errorf("failed to parse token response: %w", err)
	}

	if tokenResp.AccessToken == "" {
		return "", fmt.Errorf("no access token received in response")
	}

	return tokenResp.AccessToken, nil
}
