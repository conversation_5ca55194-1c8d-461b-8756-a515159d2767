package salesforce_account_service

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/metadiv-tech/metaorm"
	"github.com/metadiv-tech/mod_salesforce/services/salesforce_auth_service"
	"github.com/metadiv-tech/mod_salesforce/system_settings"
	"github.com/metadiv-tech/mod_utils/services/system_setting_service"
	"github.com/metadiv-tech/salesforce"
)

type UpdateAccountService struct {
	db *metaorm.DB
}

type UpdateAccountRequest struct {
	AccountId string                 `json:"account_id" binding:"required"`
	Updates   map[string]interface{} `json:"updates" binding:"required"`
}

type UpdateAccountResponse struct {
	Success   bool   `json:"success"`
	AccountId string `json:"account_id"`
	Message   string `json:"message,omitempty"`
}

// NewUpdateAccountService creates a new instance of the update account service
func NewUpdateAccountService(db *metaorm.DB) *UpdateAccountService {
	return &UpdateAccountService{
		db: db,
	}
}

// getSalesforceClient returns a Salesforce client with a fresh access token
func (s *UpdateAccountService) getSalesforceClient(workspaceId uint) (*salesforce.Client, error) {
	// Get system settings using workspace query
	workspaceQuery := system_setting_service.NewWorkspaceQuery(s.db, workspaceId)

	instance := workspaceQuery.GetByKey(system_settings.KeySalesforceInstance).String()
	if instance == "" {
		return nil, fmt.Errorf("SALESFORCE_INSTANCE must be configured in system settings")
	}

	// Get a fresh access token using the existing refresh token service
	accessToken, err := salesforce_auth_service.RefreshAccessToken(s.db, workspaceId)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh access token: %w", err)
	}

	// Create and return a Salesforce client
	client, err := salesforce.NewClient(accessToken, instance)
	if err != nil {
		return nil, fmt.Errorf("failed to create Salesforce client: %w", err)
	}

	return client, nil
}

// makeSalesforceRequest makes a direct API request to Salesforce with token refresh
func (s *UpdateAccountService) makeSalesforceRequest(workspaceId uint, method, endpoint string, body []byte) (*http.Response, error) {
	// Get system settings using workspace query
	workspaceQuery := system_setting_service.NewWorkspaceQuery(s.db, workspaceId)

	instance := workspaceQuery.GetByKey(system_settings.KeySalesforceInstance).String()
	if instance == "" {
		return nil, fmt.Errorf("SALESFORCE_INSTANCE must be configured in system settings")
	}

	// Get a fresh access token using the existing refresh token service
	accessToken, err := salesforce_auth_service.RefreshAccessToken(s.db, workspaceId)
	if err != nil {
		return nil, fmt.Errorf("failed to refresh access token: %w", err)
	}

	// Build the full URL
	fullURL := fmt.Sprintf("%s%s", instance, endpoint)

	// Create the request
	var req *http.Request
	if body != nil {
		req, err = http.NewRequest(method, fullURL, bytes.NewReader(body))
	} else {
		req, err = http.NewRequest(method, fullURL, nil)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	// Set headers
	req.Header.Set("Authorization", "Bearer "+accessToken)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	// Make the request
	httpClient := &http.Client{}
	resp, err := httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make request: %w", err)
	}

	return resp, nil
}

// UpdateAccount updates a Salesforce account with the provided field updates
// This method automatically refreshes the access token before making the API call
func (s *UpdateAccountService) UpdateAccount(workspaceId uint, request UpdateAccountRequest) (*UpdateAccountResponse, error) {
	// Validate input
	if request.AccountId == "" {
		return &UpdateAccountResponse{
			Success:   false,
			AccountId: request.AccountId,
			Message:   "Account ID is required",
		}, nil
	}

	if len(request.Updates) == 0 {
		return &UpdateAccountResponse{
			Success:   false,
			AccountId: request.AccountId,
			Message:   "At least one field update is required",
		}, nil
	}

	// Get Salesforce client (this automatically handles token refresh)
	client, err := s.getSalesforceClient(workspaceId)
	if err != nil {
		return &UpdateAccountResponse{
			Success:   false,
			AccountId: request.AccountId,
			Message:   fmt.Sprintf("Failed to get Salesforce client: %v", err),
		}, nil
	}

	// Update the account using the Salesforce client
	response, err := client.UpdateAccount(request.AccountId, request.Updates)
	if err != nil {
		return &UpdateAccountResponse{
			Success:   false,
			AccountId: request.AccountId,
			Message:   fmt.Sprintf("Failed to update account: %v", err),
		}, nil
	}

	// Check if the update was successful
	if response == nil {
		return &UpdateAccountResponse{
			Success:   false,
			AccountId: request.AccountId,
			Message:   "Update response was empty",
		}, nil
	}

	return &UpdateAccountResponse{
		Success:   true,
		AccountId: request.AccountId,
		Message:   "Account updated successfully",
	}, nil
}

// UpdateAccountField updates a specific field on a Salesforce account
// This is a convenience method for updating a single field
func (s *UpdateAccountService) UpdateAccountField(workspaceId uint, accountId, fieldName string, fieldValue interface{}) (*UpdateAccountResponse, error) {
	updates := map[string]interface{}{
		fieldName: fieldValue,
	}

	request := UpdateAccountRequest{
		AccountId: accountId,
		Updates:   updates,
	}

	return s.UpdateAccount(workspaceId, request)
}

// UpdateAccountWithRawAPI makes a direct API call to update an account using the raw Salesforce API
// This method provides more control over the API call if needed
func (s *UpdateAccountService) UpdateAccountWithRawAPI(workspaceId uint, accountId string, updates map[string]interface{}) (*UpdateAccountResponse, error) {
	// Prepare the API endpoint
	endpoint := fmt.Sprintf("/services/data/v57.0/sobjects/Account/%s", accountId)

	// Convert updates to JSON
	updateBody, err := json.Marshal(updates)
	if err != nil {
		return &UpdateAccountResponse{
			Success:   false,
			AccountId: accountId,
			Message:   fmt.Sprintf("Failed to marshal update data: %v", err),
		}, nil
	}

	// Make the API request (this automatically handles token refresh)
	resp, err := s.makeSalesforceRequest(workspaceId, "PATCH", endpoint, updateBody)
	if err != nil {
		return &UpdateAccountResponse{
			Success:   false,
			AccountId: accountId,
			Message:   fmt.Sprintf("Failed to make API request: %v", err),
		}, nil
	}
	defer resp.Body.Close()

	// Check if the update was successful
	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		// Try to read error response
		body, _ := io.ReadAll(resp.Body)
		return &UpdateAccountResponse{
			Success:   false,
			AccountId: accountId,
			Message:   fmt.Sprintf("API request failed with status code: %d, response: %s", resp.StatusCode, string(body)),
		}, nil
	}

	return &UpdateAccountResponse{
		Success:   true,
		AccountId: accountId,
		Message:   "Account updated successfully via raw API",
	}, nil
}
